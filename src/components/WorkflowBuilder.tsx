import React, {useCallback, useEffect, useRef, useState} from 'react'
import {
  ReactFlow,
  Background,
  Controls,
  MiniMap,
  ReactFlowProvider,
  useReactFlow,
} from '@xyflow/react'
import '@xyflow/react/dist/style.css'

import { useWorkflow } from '../hooks/useWorkflow'
import { NodeType } from '../types/workflow'
import Sidebar from './Sidebar'
import StartNode from './nodes/StartNode'
import ProcessNode from './nodes/ProcessNode'
import DecisionNode from './nodes/DecisionNode'
import EndNode from './nodes/EndNode'

const nodeTypes = {
  start: StartNode,
  process: ProcessNode,
  decision: DecisionNode,
  end: EndNode,
}

const WorkflowBuilderContent: React.FC = () => {
  const reactFlowWrapper = useRef<HTMLDivElement>(null)
  const { screenToFlowPosition } = useReactFlow()
  const [saveDialogOpen, setSaveDialogOpen] = useState(false)
  const [workflowName, setWorkflowName] = useState('')
  const [workflowDescription, setWorkflowDescription] = useState('')

  const {
    nodes,
    edges,
    onNodesChange,
    onEdgesChange,
    onConnect,
    addNode,
    clearWorkflow,
    saveWorkflow,
    validateCurrentWorkflow,
    getWorkflowFromLocalStorage,
  } = useWorkflow()

  const validationErrors = validateCurrentWorkflow()

  useEffect(() => {
    getWorkflowFromLocalStorage()
  }, [])

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    event.dataTransfer.dropEffect = 'move'
  }, [])

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault()

      const type = event.dataTransfer.getData('application/reactflow') as NodeType
      if (!type) return

      const position = screenToFlowPosition({
        x: event.clientX,
        y: event.clientY,
      })

      addNode(type, position)
    },
    [screenToFlowPosition, addNode]
  )

  const handleAddNode = useCallback(
    (type: NodeType) => {
      // Add node at center of viewport
      const position = { x: 250, y: 250 }
      addNode(type, position)
    },
    [addNode]
  )

  const handleSaveWorkflow = useCallback(() => {
    if (validationErrors.length > 0) {
      alert('Please fix validation errors before saving')
      return
    }
    setSaveDialogOpen(true)
  }, [validationErrors])

  const handleSaveConfirm = useCallback(() => {
    if (!workflowName.trim()) {
      alert('Please enter a workflow name')
      return
    }
    
    saveWorkflow(workflowName, workflowDescription)
    setSaveDialogOpen(false)
    setWorkflowName('')
    setWorkflowDescription('')
    alert('Workflow saved successfully!')
  }, [workflowName, workflowDescription, saveWorkflow])

  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Delete') {
      // Handle node deletion - this would need to be implemented with selected nodes
      console.log('Delete key pressed')
    }
  }, [])

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar
        onAddNode={handleAddNode}
        onClearWorkflow={clearWorkflow}
        onSaveWorkflow={handleSaveWorkflow}
        validationErrors={validationErrors}
      />
      
      <div className="flex-1" ref={reactFlowWrapper}>
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          onDrop={onDrop}
          onDragOver={onDragOver}
          onKeyDown={handleKeyDown}
          nodeTypes={nodeTypes}
          fitView
          attributionPosition="top-right"
        >
          <Background color="#aaa" gap={16} />
          <Controls />
          <MiniMap />
        </ReactFlow>
      </div>

      {/* Save Dialog */}
      {saveDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl w-96">
            <h3 className="text-lg font-semibold mb-4">Save Workflow</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Workflow Name *
                </label>
                <input
                  type="text"
                  value={workflowName}
                  onChange={(e) => setWorkflowName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter workflow name"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  value={workflowDescription}
                  onChange={(e) => setWorkflowDescription(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="Enter workflow description"
                />
              </div>
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setSaveDialogOpen(false)}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveConfirm}
                className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

const WorkflowBuilder: React.FC = () => {
  return (
    <ReactFlowProvider>
      <WorkflowBuilderContent />
    </ReactFlowProvider>
  )
}

export default WorkflowBuilder
