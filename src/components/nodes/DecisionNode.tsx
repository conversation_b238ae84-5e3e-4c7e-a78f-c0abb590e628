import React from 'react'
import { NodeProps } from '@xyflow/react'
import { BaseNode } from './BaseNode'
import { WorkflowNodeData } from '../../types/workflow'

const DecisionNode: React.FC<NodeProps<WorkflowNodeData>> = (props) => {
  return (
    <BaseNode
      {...props}
      icon="❓"
      color="yellow"
      shape="diamond"
      onDelete={props.data?.onDelete}
    />
  )
}

export default DecisionNode
