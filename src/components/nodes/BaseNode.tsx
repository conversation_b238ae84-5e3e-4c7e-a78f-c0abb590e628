import {ReactNode, useState} from "react";
import {Hand<PERSON>, NodeProps, Position} from "@xyflow/react";

interface BaseNodeProps extends NodeProps {
    icon?: string
    onDelete?: (id: string) => void
    children?: ReactNode
    color?: string // Tailwind color prefix: green, yellow, etc.
    shape?: 'default' | 'diamond'
}

export const BaseNode = ({
     id,
     data,
     selected,
     icon = '🔷',
     onDelete,
     children,
     color = 'gray',
     shape = 'default'
 }: BaseNodeProps) => {
    const [hovered, setHovered] = useState(false)
    const showDelete = hovered || selected
    const isDiamond = shape === 'diamond'

    return (
        <div
            onMouseEnter={() => setHovered(true)}
            onMouseLeave={() => setHovered(false)}
            className={`relative text-center ${
                isDiamond ? 'w-[120px] h-[120px]' : 'min-w-[150px]'
            }`}
        >
            <div
                className={`
        absolute inset-0
        ${isDiamond ? 'transform rotate-45 bg-' + color + '-100 border border-' + color + '-500' : 'bg-' + color + '-100 border border-' + color + '-500'}
        rounded p-3 shadow-md
        flex flex-col justify-center items-center
        transition-all
      `}
            >
                {/* Delete Icon */}
                {showDelete && (
                    <div
                        onClick={() => onDelete?.(id)}
                        className="absolute -top-3 -right-3 bg-red-500 text-white w-6 h-6 rounded-full flex items-center justify-center text-sm cursor-pointer shadow"
                    >
                        ×
                    </div>
                )}

                {/* Nội dung bên trong (giữ nguyên góc nhìn) */}
                <div className={`${isDiamond ? 'transform -rotate-45' : ''}`}>
                    <div className="font-semibold mb-2">{icon} {data.label}</div>
                    {children}
                </div>
            </div>

            {/* Handles */}
            <Handle type="target" position={Position.Left}/>
            <Handle type="source" position={Position.Right}/>
        </div>
    )
}